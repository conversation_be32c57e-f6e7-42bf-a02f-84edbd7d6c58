# Postman文件上传接口测试指南

## 接口信息
- **URL**: `http://localhost:8181/api/v1/files/upload`
- **方法**: `POST`
- **Content-Type**: `multipart/form-data`

## Postman配置步骤

### 1. 基本设置
1. 创建新的POST请求
2. 输入URL: `http://localhost:8181/api/v1/files/upload`
3. 选择Body标签页
4. 选择`form-data`选项

### 2. 单文件上传测试

#### 参数配置：
| Key | Type | Value | Description |
|-----|------|-------|-------------|
| files | File | 选择本地文件 | 要上传的文件 |
| destPaths | Text | `C:\temp\uploaded_file.txt` | 目标保存路径 |

#### 预期响应：
```json
{
    "code": 0,
    "message": "文件全部保存成功",
    "data": null
}
```

### 3. 多文件上传测试

#### 参数配置：
| Key | Type | Value | Description |
|-----|------|-------|-------------|
| files | File | 选择第一个文件 | 第一个文件 |
| files | File | 选择第二个文件 | 第二个文件 |
| destPaths | Text | `C:\temp\file1.txt` | 第一个文件的目标路径 |
| destPaths | Text | `C:\temp\file2.txt` | 第二个文件的目标路径 |

**注意**: 在Postman中，要添加多个相同key的参数，需要点击key右侧的"+"按钮添加新行。

### 4. 测试用例

#### 测试用例1: 成功上传单个文本文件
```
files: test.txt (内容: "Hello World")
destPaths: C:\temp\uploaded_test.txt
```

#### 测试用例2: 成功上传多个文件
```
files: file1.txt, file2.txt
destPaths: C:\temp\file1.txt, C:\temp\file2.txt
```

#### 测试用例3: 上传图片文件
```
files: image.jpg
destPaths: C:\temp\uploaded_image.jpg
```

#### 测试用例4: 错误测试 - 文件过大
```
files: large_file.txt (大于1MB)
destPaths: C:\temp\large.txt
预期响应: 400错误，"文件超出大小限制"
```

#### 测试用例5: 错误测试 - 目标目录不存在
```
files: test.txt
destPaths: C:\non_existent_dir\test.txt
预期响应: 400错误，"目标目录不存在"
```

#### 测试用例6: 错误测试 - 文件和路径数量不匹配
```
files: file1.txt, file2.txt
destPaths: C:\temp\file1.txt
预期响应: 400错误，"destPaths数量需与files一致"
```

#### 测试用例7: 错误测试 - 无文件上传
```
destPaths: C:\temp\test.txt
预期响应: 400错误，"至少上传一个文件"
```

## 注意事项

### 1. 目标路径要求
- 目标目录必须存在
- 建议使用绝对路径
- Windows路径示例: `C:\temp\filename.txt`
- Linux路径示例: `/tmp/filename.txt`

### 2. 文件限制
- 单个文件最大1MB
- 单次请求最多5个文件
- 支持任意文件类型

### 3. 测试前准备
1. 确保应用已启动 (`mvn spring-boot:run`)
2. 确保目标目录存在 (如 `C:\temp\`)
3. 准备测试文件

### 4. 响应格式
成功响应:
```json
{
    "code": 0,
    "message": "文件全部保存成功",
    "data": null
}
```

错误响应:
```json
{
    "code": 400,
    "message": "具体错误信息",
    "data": null
}
```

## cURL命令示例

### 单文件上传
```bash
curl -X POST \
  http://localhost:8181/api/v1/files/upload \
  -H 'Content-Type: multipart/form-data' \
  -F 'files=@/path/to/your/file.txt' \
  -F 'destPaths=C:\temp\uploaded_file.txt'
```

### 多文件上传
```bash
curl -X POST \
  http://localhost:8181/api/v1/files/upload \
  -H 'Content-Type: multipart/form-data' \
  -F 'files=@/path/to/file1.txt' \
  -F 'files=@/path/to/file2.txt' \
  -F 'destPaths=C:\temp\file1.txt' \
  -F 'destPaths=C:\temp\file2.txt'
```

## 故障排除

### 常见问题
1. **"目标目录不存在"** - 检查destPaths中的目录是否存在
2. **"文件超出大小限制"** - 确保文件小于1MB
3. **"destPaths数量需与files一致"** - 确保文件数量和路径数量相同
4. **连接拒绝** - 确保应用已启动且端口正确

### 调试建议
1. 先测试简单的单文件上传
2. 使用小文件进行测试
3. 检查应用日志获取详细错误信息
4. 确保有足够的磁盘空间和写入权限
