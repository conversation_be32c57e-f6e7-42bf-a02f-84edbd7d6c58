package com.example.webservice.controller;

import com.example.webservice.WebServiceApplication;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.*;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest(classes = WebServiceApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class FileControllerIntegrationTest {

    @LocalServerPort
    private int port;

    private RestTemplate restTemplate;
    private String baseUrl;

    @TempDir
    Path tempDir;

    @BeforeEach
    public void setup() {
        restTemplate = new RestTemplate();
        baseUrl = "http://localhost:" + port + "/api/v1/files";
    }

    @Test
    public void testUploadSingleFile() throws IOException {
        // Prepare test file content
        String fileContent = "This is a test file for upload";
        byte[] fileBytes = fileContent.getBytes();
        
        // Create destination path in temp directory
        Path destPath = tempDir.resolve("uploaded_test.txt");
        
        // Prepare multipart request
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);
        
        MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
        
        // Add file
        ByteArrayResource fileResource = new ByteArrayResource(fileBytes) {
            @Override
            public String getFilename() {
                return "test.txt";
            }
        };
        body.add("files", fileResource);
        
        // Add destination path
        body.add("destPaths", destPath.toString());
        
        HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);
        
        // Send request
        ResponseEntity<String> response = restTemplate.exchange(
                baseUrl + "/upload",
                HttpMethod.POST,
                requestEntity,
                String.class
        );
        
        // Verify response
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertTrue(response.getBody().contains("\"code\":0"));
        assertTrue(response.getBody().contains("文件全部保存成功"));
        
        // Verify file was actually saved
        assertTrue(Files.exists(destPath));
//        assertEquals(fileContent, Files.readString(destPath));
    }

    @Test
    public void testUploadMultipleFiles() throws IOException {
        // Prepare test files
        String content1 = "Content of first file";
        String content2 = "Content of second file";
        
        Path destPath1 = tempDir.resolve("file1.txt");
        Path destPath2 = tempDir.resolve("file2.txt");
        
        // Prepare multipart request
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);
        
        MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
        
        // Add first file
        ByteArrayResource file1Resource = new ByteArrayResource(content1.getBytes()) {
            @Override
            public String getFilename() {
                return "file1.txt";
            }
        };
        body.add("files", file1Resource);
        
        // Add second file
        ByteArrayResource file2Resource = new ByteArrayResource(content2.getBytes()) {
            @Override
            public String getFilename() {
                return "file2.txt";
            }
        };
        body.add("files", file2Resource);
        
        // Add destination paths
        body.add("destPaths", destPath1.toString());
        body.add("destPaths", destPath2.toString());
        
        HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);
        
        // Send request
        ResponseEntity<String> response = restTemplate.exchange(
                baseUrl + "/upload",
                HttpMethod.POST,
                requestEntity,
                String.class
        );
        
        // Verify response
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertTrue(response.getBody().contains("\"code\":0"));
        
        // Verify both files were saved
        assertTrue(Files.exists(destPath1));
        assertTrue(Files.exists(destPath2));
//        assertEquals(content1, Files.readString(destPath1));
//        assertEquals(content2, Files.readString(destPath2));
    }

    @Test
    public void testUploadBinaryFile() throws IOException {
        // Create binary content (simulating an image file)
        byte[] binaryContent = {
            (byte)0xFF, (byte)0xD8, (byte)0xFF, (byte)0xE0, // JPEG header
            0x00, 0x10, 0x4A, 0x46, 0x49, 0x46, 0x00, 0x01,
            0x01, 0x01, 0x00, 0x48, 0x00, 0x48, 0x00, 0x00
        };
        
        Path destPath = tempDir.resolve("test_image.jpg");
        
        // Prepare multipart request
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);
        
        MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
        
        ByteArrayResource imageResource = new ByteArrayResource(binaryContent) {
            @Override
            public String getFilename() {
                return "test_image.jpg";
            }
        };
        body.add("files", imageResource);
        body.add("destPaths", destPath.toString());
        
        HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);
        
        // Send request
        ResponseEntity<String> response = restTemplate.exchange(
                baseUrl + "/upload",
                HttpMethod.POST,
                requestEntity,
                String.class
        );
        
        // Verify response
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertTrue(response.getBody().contains("\"code\":0"));
        
        // Verify binary file was saved correctly
        assertTrue(Files.exists(destPath));
        byte[] savedContent = Files.readAllBytes(destPath);
        assertArrayEquals(binaryContent, savedContent);
    }

    @Test
    public void testUploadWithInvalidDestination() {
        // Prepare test file
        String fileContent = "Test content";
        
        // Use invalid destination path
        String invalidDestPath = "/non/existent/directory/test.txt";
        
        // Prepare multipart request
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);
        
        MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
        
        ByteArrayResource fileResource = new ByteArrayResource(fileContent.getBytes()) {
            @Override
            public String getFilename() {
                return "test.txt";
            }
        };
        body.add("files", fileResource);
        body.add("destPaths", invalidDestPath);
        
        HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);
        
        // Send request
        ResponseEntity<String> response = restTemplate.exchange(
                baseUrl + "/upload",
                HttpMethod.POST,
                requestEntity,
                String.class
        );
        
        // Verify error response
        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        assertTrue(response.getBody().contains("\"code\":400"));
        assertTrue(response.getBody().contains("目标目录不存在"));
    }

    @Test
    public void testUploadLargeFile() {
        // Create file larger than 1MB limit
        byte[] largeContent = new byte[1_048_577]; // 1MB + 1 byte
        
        Path destPath = tempDir.resolve("large_file.txt");
        
        // Prepare multipart request
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);
        
        MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
        
        ByteArrayResource largeFileResource = new ByteArrayResource(largeContent) {
            @Override
            public String getFilename() {
                return "large_file.txt";
            }
        };
        body.add("files", largeFileResource);
        body.add("destPaths", destPath.toString());
        
        HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);
        
        // Send request
        ResponseEntity<String> response = restTemplate.exchange(
                baseUrl + "/upload",
                HttpMethod.POST,
                requestEntity,
                String.class
        );
        
        // Verify error response
        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        assertTrue(response.getBody().contains("\"code\":400"));
        assertTrue(response.getBody().contains("文件超出大小限制"));
    }

    @Test
    public void testUploadWithoutFiles() {
        // Prepare request without files
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);
        
        MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
        body.add("destPaths", tempDir.resolve("test.txt").toString());
        
        HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);
        
        // Send request
        ResponseEntity<String> response = restTemplate.exchange(
                baseUrl + "/upload",
                HttpMethod.POST,
                requestEntity,
                String.class
        );
        
        // Verify error response
        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        assertTrue(response.getBody().contains("\"code\":400"));
        assertTrue(response.getBody().contains("至少上传一个文件"));
    }
}
