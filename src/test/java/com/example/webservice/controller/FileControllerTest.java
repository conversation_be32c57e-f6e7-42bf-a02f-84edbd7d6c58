package com.example.webservice.controller;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.multipart;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

public class FileControllerTest {

    private MockMvc mockMvc;
    private FileController controller;

    @TempDir
    Path tempDir;

    @BeforeEach
    public void setup() {
        controller = new FileController();
        mockMvc = MockMvcBuilders.standaloneSetup(controller).build();
    }

    @Test
    public void upload_singleFile_shouldSucceed() throws Exception {
        // Create test file content
        String fileContent = "This is a test file content";
        MockMultipartFile file = new MockMultipartFile(
                "files", 
                "test.txt", 
                MediaType.TEXT_PLAIN_VALUE, 
                fileContent.getBytes()
        );

        // Create destination path in temp directory
        Path destPath = tempDir.resolve("uploaded_test.txt");
        
        mockMvc.perform(multipart("/api/v1/files/upload")
                        .file(file)
                        .param("destPaths", destPath.toString()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.message").value("文件全部保存成功"));

        // Verify file was actually saved
        assert Files.exists(destPath);
        assert Files.readString(destPath).equals(fileContent);
    }

    @Test
    public void upload_multipleFiles_shouldSucceed() throws Exception {
        // Create multiple test files
        MockMultipartFile file1 = new MockMultipartFile(
                "files", 
                "test1.txt", 
                MediaType.TEXT_PLAIN_VALUE, 
                "Content of file 1".getBytes()
        );
        
        MockMultipartFile file2 = new MockMultipartFile(
                "files", 
                "test2.txt", 
                MediaType.TEXT_PLAIN_VALUE, 
                "Content of file 2".getBytes()
        );

        // Create destination paths
        Path destPath1 = tempDir.resolve("uploaded_test1.txt");
        Path destPath2 = tempDir.resolve("uploaded_test2.txt");
        
        mockMvc.perform(multipart("/api/v1/files/upload")
                        .file(file1)
                        .file(file2)
                        .param("destPaths", destPath1.toString())
                        .param("destPaths", destPath2.toString()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.message").value("文件全部保存成功"));

        // Verify both files were saved
        assert Files.exists(destPath1);
        assert Files.exists(destPath2);
        assert Files.readString(destPath1).equals("Content of file 1");
        assert Files.readString(destPath2).equals("Content of file 2");
    }

    @Test
    public void upload_noFiles_shouldReturnBadRequest() throws Exception {
        mockMvc.perform(multipart("/api/v1/files/upload")
                        .param("destPaths", tempDir.resolve("test.txt").toString()))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.message").value("至少上传一个文件"));
    }

    @Test
    public void upload_tooManyFiles_shouldReturnBadRequest() throws Exception {
        // Create 6 files (exceeds MAX_FILES_PER_REQUEST = 5)
        MockMultipartFile[] files = new MockMultipartFile[6];
        String[] destPaths = new String[6];
        
        for (int i = 0; i < 6; i++) {
            files[i] = new MockMultipartFile(
                    "files", 
                    "test" + i + ".txt", 
                    MediaType.TEXT_PLAIN_VALUE, 
                    ("Content " + i).getBytes()
            );
            destPaths[i] = tempDir.resolve("test" + i + ".txt").toString();
        }

        var requestBuilder = multipart("/api/v1/files/upload");
        for (MockMultipartFile file : files) {
            requestBuilder.file(file);
        }
        for (String destPath : destPaths) {
            requestBuilder.param("destPaths", destPath);
        }

        mockMvc.perform(requestBuilder)
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.message").value("单次最多上传5个文件"));
    }

    @Test
    public void upload_fileSizeExceedsLimit_shouldReturnBadRequest() throws Exception {
        // Create a file larger than 1MB
        byte[] largeContent = new byte[1_048_577]; // 1MB + 1 byte
        MockMultipartFile largeFile = new MockMultipartFile(
                "files", 
                "large.txt", 
                MediaType.TEXT_PLAIN_VALUE, 
                largeContent
        );

        Path destPath = tempDir.resolve("large.txt");
        
        mockMvc.perform(multipart("/api/v1/files/upload")
                        .file(largeFile)
                        .param("destPaths", destPath.toString()))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.message").value("上传失败: 文件超出大小限制(<=1MB): large.txt"));
    }

    @Test
    public void upload_mismatchedDestPathsCount_shouldReturnBadRequest() throws Exception {
        MockMultipartFile file = new MockMultipartFile(
                "files", 
                "test.txt", 
                MediaType.TEXT_PLAIN_VALUE, 
                "test content".getBytes()
        );

        // Provide 2 destPaths for 1 file
        mockMvc.perform(multipart("/api/v1/files/upload")
                        .file(file)
                        .param("destPaths", tempDir.resolve("test1.txt").toString())
                        .param("destPaths", tempDir.resolve("test2.txt").toString()))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.message").value("destPaths数量需与files一致"));
    }

    @Test
    public void upload_invalidDestinationDirectory_shouldReturnBadRequest() throws Exception {
        MockMultipartFile file = new MockMultipartFile(
                "files", 
                "test.txt", 
                MediaType.TEXT_PLAIN_VALUE, 
                "test content".getBytes()
        );

        // Use non-existent directory
        Path invalidPath = Paths.get("/non/existent/directory/test.txt");
        
        mockMvc.perform(multipart("/api/v1/files/upload")
                        .file(file)
                        .param("destPaths", invalidPath.toString()))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.message").doesNotExist().or(jsonPath("$.message").value("上传失败: 目标目录不存在: " + invalidPath.getParent())));
    }

    @Test
    public void upload_emptyFile_shouldReturnBadRequest() throws Exception {
        MockMultipartFile emptyFile = new MockMultipartFile(
                "files", 
                "empty.txt", 
                MediaType.TEXT_PLAIN_VALUE, 
                new byte[0]
        );

        Path destPath = tempDir.resolve("empty.txt");
        
        mockMvc.perform(multipart("/api/v1/files/upload")
                        .file(emptyFile)
                        .param("destPaths", destPath.toString()))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.message").value("上传失败: 存在空文件"));
    }

    @Test
    public void upload_binaryFile_shouldSucceed() throws Exception {
        // Test with binary content (simulating image or other binary file)
        byte[] binaryContent = {
            (byte)0xFF, (byte)0xD8, (byte)0xFF, (byte)0xE0, // JPEG header
            0x00, 0x10, 0x4A, 0x46, 0x49, 0x46, 0x00, 0x01
        };
        
        MockMultipartFile binaryFile = new MockMultipartFile(
                "files", 
                "test.jpg", 
                MediaType.IMAGE_JPEG_VALUE, 
                binaryContent
        );

        Path destPath = tempDir.resolve("uploaded_image.jpg");
        
        mockMvc.perform(multipart("/api/v1/files/upload")
                        .file(binaryFile)
                        .param("destPaths", destPath.toString()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.message").value("文件全部保存成功"));

        // Verify binary content was preserved
        assert Files.exists(destPath);
        byte[] savedContent = Files.readAllBytes(destPath);
        assert java.util.Arrays.equals(binaryContent, savedContent);
    }
}
