package com.example.webservice.controller;

import com.example.webservice.dto.ClusterConfigDTO;
import com.example.webservice.service.ClusterConfigFileService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.http.MediaType;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

public class ClusterConfigControllerTest {

    private MockMvc mockMvc;
    private ClusterConfigFileService mockService;
    private ClusterConfigController controller;

    @BeforeEach
    public void setup() {
        controller = new ClusterConfigController();
        mockService = Mockito.mock(ClusterConfigFileService.class);
        // 将控制器中的私有final服务字段替换为mock
        ReflectionTestUtils.setField(controller, "fileService", mockService);
        mockMvc = MockMvcBuilders.standaloneSetup(controller).build();
    }

    @Test
    public void get_shouldReturnAllPlatforms() throws Exception {
        List<Map<String, String>> platforms = new ArrayList<Map<String, String>>();
        Map<String, String> p1 = new LinkedHashMap<String, String>();
        p1.put("name", "平台A");
        p1.put("id", "201");
        p1.put("nicid", "eth3");
        p1.put("virtualip", "*************");
        p1.put("netmask", "*************");
        platforms.add(p1);

        when(mockService.readAllPlatforms()).thenReturn(platforms);

        mockMvc.perform(get("/api/v1/clusters"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(1))
                .andExpect(jsonPath("$.data[0].id").value("201"))
                .andExpect(jsonPath("$.data[0].nicid").value("eth3"))
                .andExpect(jsonPath("$.data[0].virtualip").value("*************"))
                .andExpect(jsonPath("$.data[0].netmask").value("*************"));

        verify(mockService).readAllPlatforms();
    }

    @Test
    public void post_shouldWriteAndReturnSuccess() throws Exception {
        String body = "{\"mode\":\"nor\",\"id\":\"201\",\"nicid\":\"eth3\",\"virtualip\":\"*************\",\"netmask\":\"*************\"}";

        mockMvc.perform(post("/api/v1/clusters")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(body))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.message").value("保存成功"));

        verify(mockService).writeFromDto(Mockito.any(ClusterConfigDTO.class));
    }
}


