server:
  port: 8181

spring:
  application:
    name: webservice

management:
  endpoints:
    web:
      exposure:
        include: health,info

# 日志配置
logging:
  level:
    root: INFO
    com.example.webservice: DEBUG
    org.springframework.web: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: ./logs/webservice.log
  logback:
    rollingpolicy:
      max-file-size: 10MB
      max-history: 30
      total-size-cap: 1GB

