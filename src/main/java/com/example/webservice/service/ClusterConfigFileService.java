package com.example.webservice.service;

import com.example.webservice.dto.ClusterConfigDTO;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.List;
import java.util.ArrayList;
import java.util.Properties;
import java.net.NetworkInterface;
import java.net.InetAddress;
import java.net.Inet4Address;
import java.util.Enumeration;
import java.util.HashMap;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;
import javax.xml.transform.OutputKeys;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerException;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;

import com.example.webservice.util.HttpClientUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import org.xml.sax.SAXException;

public class ClusterConfigFileService {
	private static final Logger logger = LoggerFactory.getLogger(ClusterConfigFileService.class);
	private static final Path CONFIG_PATH = Paths.get("/etc/unimas/tomcat/conf/platforms.xml");
	private static final Path FLAG_PATH = Paths.get("/etc/unimas/tomcat/conf/platforms_used.flag");
	public Map<String, String> readRaw() throws IOException {
		Map<String, String> map = new LinkedHashMap<String, String>();
		if (!Files.exists(CONFIG_PATH)) {
			return map;
		}
		try {
			Document doc = parseXml();
			Element firstPlatform = getFirstPlatform(doc);
			if (firstPlatform == null) {
				return map;
			}
			String mode = getChildText(firstPlatform, "mode");
			String id = getChildText(firstPlatform, "id");
			String nicid = getChildText(firstPlatform, "nicid");
			String virtualip = getChildText(firstPlatform, "virtualip");
			String netmask = getChildText(firstPlatform, "netmask");

			map.put("mode", mode == null ? "" : mode.trim());
			map.put("id", id == null ? "" : id.trim());
			map.put("nicid", nicid == null ? "" : nicid.trim());
			map.put("virtualip", virtualip == null ? "" : virtualip.trim());
			map.put("netmask", (netmask == null || netmask.trim().isEmpty()) ? "*************" : netmask.trim());
			return map;
		} catch (ParserConfigurationException | SAXException e) {
			throw new IOException("解析XML失败: " + e.getMessage(), e);
		}
	}

	public List<Map<String, String>> readAllPlatforms() throws IOException {
		List<Map<String, String>> result = new ArrayList<Map<String, String>>();
		if (!Files.exists(CONFIG_PATH)) {
			return result;
		}
		try {
			Document doc = parseXml();
			NodeList platforms = doc.getElementsByTagName("platform");
			for (int i = 0; i < platforms.getLength(); i++) {
				Element platform = (Element) platforms.item(i);
				Map<String, String> map = new LinkedHashMap<String, String>();
				String name = getChildText(platform, "name");
				String devid = getChildText(platform, "devid");
				String address = getChildText(platform, "address");
				String port = getChildText(platform, "port");
				String mode = getChildText(platform, "mode");
				String id = getChildText(platform, "id");
				String nicid = getChildText(platform, "nicid");
				String virtualip = getChildText(platform, "virtualip");
				String netmask = getChildText(platform, "netmask");

				map.put("name", name == null ? "" : name.trim());
				map.put("devid", devid == null ? "" : devid.trim());
				map.put("address", address == null ? "" : address.trim());
				map.put("port", port == null ? "" : port.trim());
				map.put("mode", mode == null ? "" : mode.trim());
				map.put("id", id == null ? "" : id.trim());
				map.put("nicid", nicid == null ? "" : nicid.trim());
				map.put("virtualip", virtualip == null ? "" : virtualip.trim());
				map.put("netmask", (netmask == null || netmask.trim().isEmpty()) ? "*************" : netmask.trim());

				result.add(map);
			}
			return result;
		} catch (ParserConfigurationException | SAXException e) {
			throw new IOException("解析XML失败: " + e.getMessage(), e);
		}
	}

	public void writeFromDto(ClusterConfigDTO dto) throws IOException {
		try {
			Document doc = Files.exists(CONFIG_PATH) ? parseXml() : newDocument();
			Element root = ensureRoot(doc);
			NodeList platforms = root.getElementsByTagName("platform");
			if (platforms.getLength() == 0) {
				// 如果不存在任何platform，创建一个
				root.appendChild(doc.createElement("platform"));
				platforms = root.getElementsByTagName("platform");
			}

			String mode = normalizeMode(dto.getMode());
			String id = dto.getId();
			String nicid = dto.getNicid();
			String virtualip = dto.getVirtualip();
			String netmask = (dto.getNetmask() == null || dto.getNetmask().trim().isEmpty()) ? "*************" : dto.getNetmask().trim();

			for (int i = 0; i < platforms.getLength(); i++) {
				Element platform = (Element) platforms.item(i);
				setOrCreateChild(platform, "mode", mode);
				setOrCreateChild(platform, "id", id);
				setOrCreateChild(platform, "nicid", nicid);
				setOrCreateChild(platform, "virtualip", virtualip);
				setOrCreateChild(platform, "netmask", netmask);
			}

			writeXml(doc);
		} catch (ParserConfigurationException | SAXException | TransformerException e) {
			throw new IOException("写入XML失败: " + e.getMessage(), e);
		}
	}

	private String normalizeMode(String mode) {
		if (mode == null) return "";
		mode = mode.trim().toLowerCase();
		if ("nor".equals(mode) || "main".equals(mode) || "back".equals(mode)) return mode;
		if ("independent".equals(mode)) return "nor";
		if ("master_backup".equals(mode)) return "main";
		return mode;
	}

	private Document parseXml() throws ParserConfigurationException, SAXException, IOException {
		DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
		// 安全设置，防止XXE
		factory.setFeature("http://apache.org/xml/features/disallow-doctype-decl", true);
		factory.setFeature("http://xml.org/sax/features/external-general-entities", false);
		factory.setFeature("http://xml.org/sax/features/external-parameter-entities", false);
		factory.setXIncludeAware(false);
		factory.setExpandEntityReferences(false);
		DocumentBuilder builder = factory.newDocumentBuilder();
		return builder.parse(CONFIG_PATH.toFile());
	}

	private Document newDocument() throws ParserConfigurationException {
		DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
		DocumentBuilder builder = factory.newDocumentBuilder();
		Document doc = builder.newDocument();
		Element root = doc.createElement("platforms");
		doc.appendChild(root);
		return doc;
	}

	private Element ensureRoot(Document doc) {
		NodeList roots = doc.getElementsByTagName("platforms");
		if (roots.getLength() > 0) {
			return (Element) roots.item(0);
		}
		Element root = doc.createElement("platforms");
		doc.appendChild(root);
		return root;
	}

	private Element getFirstPlatform(Document doc) {
		NodeList list = doc.getElementsByTagName("platform");
		if (list.getLength() == 0) return null;
		return (Element) list.item(0);
	}

	private String getChildText(Element parent, String tagName) {
		NodeList list = parent.getElementsByTagName(tagName);
		if (list.getLength() == 0) return null;
		Node node = list.item(0);
		return node.getTextContent();
	}

	private void setOrCreateChild(Element parent, String tagName, String text) {
		Document doc = parent.getOwnerDocument();
		NodeList list = parent.getElementsByTagName(tagName);
		Element child;
		if (list.getLength() == 0) {
			child = doc.createElement(tagName);
			parent.appendChild(child);
		} else {
			child = (Element) list.item(0);
		}
		child.setTextContent(text == null ? "" : text);
	}

	private void writeXml(Document doc) throws TransformerException, IOException {
		TransformerFactory tf = TransformerFactory.newInstance();
		Transformer transformer = tf.newTransformer();
		transformer.setOutputProperty(OutputKeys.ENCODING, "GBK");
		transformer.setOutputProperty(OutputKeys.INDENT, "yes");
		transformer.setOutputProperty("{http://xml.apache.org/xslt}indent-amount", "2");
		DOMSource source = new DOMSource(doc);
		try (java.io.OutputStream os = Files.newOutputStream(CONFIG_PATH)) {
			StreamResult result = new StreamResult(os);
			transformer.transform(source, result);
		}
	}

	// 校验平台信息：devid、brand、address（本机NIC解析）、port（直接存在即可）
	public boolean validatePlatformInfo(String name, String devid, String address, int port, String brand) {
		try {
			//TODO 0) 授权校验

			// 1) 读取 /boot/cfcard 键值
			Properties cf = loadKeyValueFile("/boot/cfcard");
			String cfDevid = trimOrEmpty(cf.getProperty("devid"));
			String cfBrand = trimOrEmpty(cf.getProperty("productBrand"));
			if (!safeEquals(cfDevid, devid)){
				logger.info("cfDevid: " + cfDevid + ", devid: " + devid);
				System.out.println("cfDevid: " + cfDevid + ", devid: " + devid);
				return false;
			}
			if (!safeEquals(cfBrand, brand)){
				logger.info("cfBrand: " + cfBrand + ", brand: " + brand);
				System.out.println("cfBrand: " + cfBrand + ", brand: " + brand);
				return false;
			}

			// 2) 读取 serviceIp.prop -> ip=网卡名

			Properties prop = loadKeyValueFile("/etc/unimas/tomcat/conf/serviceIp.prop");
			String nicName = trimOrEmpty(prop.getProperty("ip"));
			String localIp = resolveIpv4ByNic(nicName);
			if (localIp == null || localIp.isEmpty()){
				logger.error("resolveIpv4ByNic failed, nicName: " + nicName);
				System.out.println("resolveIpv4ByNic failed, nicName: " + nicName);
				return false;
			}
			if (!safeEquals(localIp, address)){
				logger.error("localIp: " + localIp + ", address: " + address);
				System.out.println("localIp: " + localIp + ", address: " + address);
				return false;
			}

			// 3) 端口无需外部校验，这里仅判存在


			if(!Files.exists(FLAG_PATH)){
				FLAG_PATH.toFile().createNewFile();
			}
			return true;
		} catch (Exception e) {
			return false;
		}
	}

	private Properties loadKeyValueFile(String path) throws IOException {
		Properties p = new Properties();
		Path f = Paths.get(path);
		if (!Files.exists(f)) return p;
		// cfcard是key=value的纯文本，使用ISO_8859_1读取再转UTF-8可能更稳，这里直接用默认
		List<String> lines = Files.readAllLines(f);
		for (String line : lines) {
			if (line == null) continue;
			line = line.trim();
			if (line.isEmpty() || line.startsWith("#")) continue;
			int idx = line.indexOf('=');
			if (idx > 0) {
				String k = line.substring(0, idx).trim();
				String v = line.substring(idx + 1).trim();
				p.setProperty(k, v);
			}
		}
		return p;
	}

	private String resolveIpv4ByNic(String nicName) throws Exception {
		if (nicName == null || nicName.isEmpty()) return null;
		NetworkInterface ni = NetworkInterface.getByName(nicName);
		if (ni == null) return null;
		Enumeration<InetAddress> addrs = ni.getInetAddresses();
		while (addrs.hasMoreElements()) {
			InetAddress addr = addrs.nextElement();
			if (addr instanceof Inet4Address) {
				return addr.getHostAddress();
			}
		}
		return null;
	}

	private String trimOrEmpty(String s) { return s == null ? "" : s.trim(); }
	private boolean safeEquals(String a, String b) { return trimOrEmpty(a).equals(trimOrEmpty(b)); }

	// 平台信息 CRUD：name、devid、address、port
	public List<Map<String, String>> listPlatformsBasic() throws IOException {
		List<Map<String, String>> result = new ArrayList<Map<String, String>>();
		if (!Files.exists(CONFIG_PATH)) return result;
		try {
			Document doc = parseXml();
			NodeList platforms = doc.getElementsByTagName("platform");
			for (int i = 0; i < platforms.getLength(); i++) {
				Element platform = (Element) platforms.item(i);
				Map<String, String> map = new LinkedHashMap<String, String>();
				map.put("name", safe(getChildText(platform, "name")));
				map.put("devid", safe(getChildText(platform, "devid")));
				map.put("address", safe(getChildText(platform, "address")));
				map.put("port", safe(getChildText(platform, "port")));
				result.add(map);
			}
			return result;
		} catch (ParserConfigurationException | SAXException e) {
			throw new IOException("解析XML失败: " + e.getMessage(), e);
		}
	}

	public Map<String, String> getPlatformByName(String name) throws IOException {
		if (!Files.exists(CONFIG_PATH)) return null;
		try {
			Document doc = parseXml();
			NodeList platforms = doc.getElementsByTagName("platform");
			for (int i = 0; i < platforms.getLength(); i++) {
				Element p = (Element) platforms.item(i);
				String n = safe(getChildText(p, "name"));
				if (n.equals(name)) {
					Map<String, String> map = new LinkedHashMap<String, String>();
					map.put("name", n);
					map.put("devid", safe(getChildText(p, "devid")));
					map.put("address", safe(getChildText(p, "address")));
					map.put("port", safe(getChildText(p, "port")));
					return map;
				}
			}
			return null;
		} catch (ParserConfigurationException | SAXException e) {
			throw new IOException("解析XML失败: " + e.getMessage(), e);
		}
	}

	public void createOrUpdatePlatform(String name, String devid, String address, int port) throws IOException {
		try {
			Document doc = Files.exists(CONFIG_PATH) ? parseXml() : newDocument();
			Element root = ensureRoot(doc);
			Element platform = findPlatformByName(root, name);
			if (platform == null) {
				platform = doc.createElement("platform");
				root.appendChild(platform);
				throw new IOException("该平台不存在！");
			}
			setOrCreateChild(platform, "name", name);
			setOrCreateChild(platform, "devid", devid);
			setOrCreateChild(platform, "address", address);
			setOrCreateChild(platform, "port", String.valueOf(port));
			writeXml(doc);
		} catch (ParserConfigurationException | SAXException | TransformerException e) {
			throw new IOException("写入XML失败: " + e.getMessage(), e);
		}
	}

	public boolean deletePlatformByName(String name) throws IOException {
		boolean ret = true;
		if(Files.exists(FLAG_PATH)){
			ret =  FLAG_PATH.toFile().delete();
		}
		return ret;
//		if (!Files.exists(CONFIG_PATH)) return false;
//		try {
//			Document doc = parseXml();
//			Element root = ensureRoot(doc);
//			Element platform = findPlatformByName(root, name);
//			if (platform == null) return false;
//			root.removeChild(platform);
//			writeXml(doc);
//			return true;
//		} catch (ParserConfigurationException | SAXException | TransformerException e) {
//			throw new IOException("写入XML失败: " + e.getMessage(), e);
//		}
	}

	private String safe(String s) { return s == null ? "" : s.trim(); }

	private Element findPlatformByName(Element root, String name) {
		NodeList platforms = root.getElementsByTagName("platform");
		for (int i = 0; i < platforms.getLength(); i++) {
			Element p = (Element) platforms.item(i);
			String n = safe(getChildText(p, "name"));
			if (n.equals(name)) return p;
		}
		return null;
	}
}


