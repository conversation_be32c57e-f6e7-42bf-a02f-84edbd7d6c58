package com.example.webservice;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

@SpringBootApplication
public class WebServiceApplication {

    private static final Logger logger = LoggerFactory.getLogger(WebServiceApplication.class);

    public static void main(String[] args) {
        logger.info("正在启动WebService应用...");
        SpringApplication.run(WebServiceApplication.class, args);
        logger.info("WebService应用启动完成！");
    }
}


