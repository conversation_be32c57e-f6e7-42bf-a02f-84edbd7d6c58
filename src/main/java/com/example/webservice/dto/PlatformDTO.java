package com.example.webservice.dto;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

public class PlatformDTO {

	@NotBlank(message = "平台名称不能为空")
	private String name;

	@NotBlank(message = "设备号不能为空")
	private String devid;

	@NotBlank(message = "IP地址不能为空")
	private String address;

	@NotNull(message = "端口不能为空")
	private Integer port;

	public String getName() { return name; }
	public void setName(String name) { this.name = name; }

	public String getDevid() { return devid; }
	public void setDevid(String devid) { this.devid = devid; }

	public String getAddress() { return address; }
	public void setAddress(String address) { this.address = address; }

	public Integer getPort() { return port; }
	public void setPort(Integer port) { this.port = port; }
}



