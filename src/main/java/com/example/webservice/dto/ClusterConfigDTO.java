package com.example.webservice.dto;

import javax.validation.constraints.NotBlank;

public class ClusterConfigDTO {

	@NotBlank(message = "模式不能为空")
	private String mode; // nor/main/back

	@NotBlank(message = "编号不能为空")
	private String id; // 编号

	@NotBlank(message = "网卡设备不能为空")
	private String nicid; // 网卡设备，例如 eth3

	@NotBlank(message = "虚拟IP不能为空")
	private String virtualip; // 虚拟IP

	private String netmask; // 子网掩码，默认 *************

	public String getMode() { return mode; }
	public void setMode(String mode) { this.mode = mode; }

	public String getId() { return id; }
	public void setId(String id) { this.id = id; }

	public String getNicid() { return nicid; }
	public void setNicid(String nicid) { this.nicid = nicid; }

	public String getVirtualip() { return virtualip; }
	public void setVirtualip(String virtualip) { this.virtualip = virtualip; }

	public String getNetmask() { return netmask; }
	public void setNetmask(String netmask) { this.netmask = netmask; }
}


