package com.example.webservice.controller;

import com.example.webservice.api.ApiResponse;
import com.example.webservice.dto.PlatformDTO;
import com.example.webservice.dto.ValidationRequestDTO;
import com.example.webservice.service.ClusterConfigFileService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/v1/platforms")
public class PlatformController {

    private final ClusterConfigFileService fileService = new ClusterConfigFileService();

    @GetMapping
    public ApiResponse<Object> list() {
        try {
            List<Map<String, String>> list = fileService.listPlatformsBasic();
            return ApiResponse.success(list);
        } catch (Exception e) {
            return ApiResponse.error(500, "读取失败: " + e.getMessage());
        }
    }

    @PostMapping("/validate")
    public ApiResponse<Boolean> validate(@Validated @RequestBody ValidationRequestDTO req) {
        try {
            boolean ok = fileService.validatePlatformInfo(req.getName(), req.getDevid(), req.getAddress(), req.getPort(), req.getBrand());
            return ApiResponse.success(ok);
        } catch (Exception e) {
            return ApiResponse.error(500, "校验失败: " + e.getMessage());
        }
    }

    @GetMapping("/{name}")
    public ApiResponse<Object> get(@PathVariable("name") String name) {
        try {
            Map<String, String> platform = fileService.getPlatformByName(name);
            if (platform == null) {
                return ApiResponse.error(404, "未找到: " + name);
            }
            return ApiResponse.success(platform);
        } catch (Exception e) {
            return ApiResponse.error(500, "读取失败: " + e.getMessage());
        }
    }

    @PostMapping
    public ApiResponse<String> create(@Validated @RequestBody PlatformDTO dto) {
        try {
            fileService.createOrUpdatePlatform(dto.getName(), dto.getDevid(), dto.getAddress(), dto.getPort());
            return ApiResponse.success("创建成功", null);
        } catch (Exception e) {
            return ApiResponse.error(500, "创建失败: " + e.getMessage());
        }
    }

    @PutMapping("/{name}")
    public ApiResponse<String> update(@PathVariable("name") String name, @Validated @RequestBody PlatformDTO dto) {
        try {
            fileService.createOrUpdatePlatform(name, dto.getDevid(), dto.getAddress(), dto.getPort());
            return ApiResponse.success("更新成功", null);
        } catch (Exception e) {
            return ApiResponse.error(500, "更新失败: " + e.getMessage());
        }
    }

    @DeleteMapping("/{name}")
    public ApiResponse<String> delete(@PathVariable("name") String name) {
        try {
            boolean removed = fileService.deletePlatformByName(name);
            if (!removed) {
                return ApiResponse.error(404, "未找到: " + name);
            }
            return ApiResponse.success("删除成功", null);
        } catch (Exception e) {
            return ApiResponse.error(500, "删除失败: " + e.getMessage());
        }
    }
}
