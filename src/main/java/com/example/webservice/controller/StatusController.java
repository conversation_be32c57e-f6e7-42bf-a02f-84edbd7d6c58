package com.example.webservice.controller;

import com.example.webservice.common.Constant;
import com.example.webservice.util.AesUtil;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/v1/status")
public class StatusController extends CommonController{

	@PostMapping(value = "/file", consumes = MediaType.APPLICATION_OCTET_STREAM_VALUE, produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
	public ResponseEntity<byte[]> queryFile(@RequestBody byte[] commandBytes) {
		byte[] plain;
		try {
			plain = AesUtil.decrypt(commandBytes);
		} catch (Exception e) {
			return ResponseEntity.status(HttpStatus.BAD_GATEWAY).body(stringBytes("解密失败: " + e.getMessage()));
		}
		return forwardPayload(plain, Constant.FILE_COMMAND_PORT);
	}
	@PostMapping(value = "/db", consumes = MediaType.APPLICATION_OCTET_STREAM_VALUE, produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
	public ResponseEntity<byte[]> queryDB(@RequestBody byte[] commandBytes) {
		byte[] plain;
		try {
			plain = AesUtil.decrypt(commandBytes);
		} catch (Exception e) {
			return ResponseEntity.status(HttpStatus.BAD_GATEWAY).body(stringBytes("解密失败: " + e.getMessage()));
		}
		return forwardPayload(plain, Constant.DB_COMMAND_PORT);
	}
	@PostMapping(value = "/mail", consumes = MediaType.APPLICATION_OCTET_STREAM_VALUE, produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
	public ResponseEntity<byte[]> queryMail(@RequestBody byte[] commandBytes) {
		byte[] plain;
		try {
			plain = AesUtil.decrypt(commandBytes);
		} catch (Exception e) {
			return ResponseEntity.status(HttpStatus.BAD_GATEWAY).body(stringBytes("解密失败: " + e.getMessage()));
		}
		return forwardPayload(plain, Constant.MAIL_COMMAND_PORT);
	}
	@PostMapping(value = "/onvif", consumes = MediaType.APPLICATION_OCTET_STREAM_VALUE, produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
	public ResponseEntity<byte[]> queryOnvif(@RequestBody byte[] commandBytes) {
		byte[] plain;
		try {
			plain = AesUtil.decrypt(commandBytes);
		} catch (Exception e) {
			return ResponseEntity.status(HttpStatus.BAD_GATEWAY).body(stringBytes("解密失败: " + e.getMessage()));
		}
		return forwardPayload(plain, Constant.ONVIF_COMMAND_PORT);
	}
	@PostMapping(value = "/mq", consumes = MediaType.APPLICATION_OCTET_STREAM_VALUE, produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
	public ResponseEntity<byte[]> queryMQ(@RequestBody byte[] commandBytes) {
		byte[] plain;
		try {
			plain = AesUtil.decrypt(commandBytes);
		} catch (Exception e) {
			return ResponseEntity.status(HttpStatus.BAD_GATEWAY).body(stringBytes("解密失败: " + e.getMessage()));
		}
		return forwardPayload(plain, Constant.MQ_COMMAND_PORT);
	}


}


