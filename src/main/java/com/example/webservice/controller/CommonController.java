package com.example.webservice.controller;

import com.example.webservice.util.AesUtil;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.InetSocketAddress;
import java.net.Socket;

public class CommonController {
    protected ResponseEntity<byte[]> forwardPayload(byte[] payload, int port) {
        try (Socket socket = new Socket()) {
            socket.connect(new InetSocketAddress("127.0.0.1", port), 5000);
            socket.setSoTimeout(15000);

            OutputStream os = socket.getOutputStream();
            os.write(payload);
            os.flush();

            InputStream is = socket.getInputStream();
            ByteArrayOutputStream buffer = new ByteArrayOutputStream();
            byte[] tmp = new byte[4096];
            int read;
            while ((read = is.read(tmp)) != -1) {
                buffer.write(tmp, 0, read);
            }

            byte[] responseBytes = buffer.toByteArray();
            responseBytes = AesUtil.encrypt(responseBytes);
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            return new ResponseEntity<byte[]>(responseBytes, headers, HttpStatus.OK);
        } catch (Exception exception) {
            return ResponseEntity.status(HttpStatus.BAD_GATEWAY).body(stringBytes("Socket转发失败: " + exception.getMessage()));
        }
    }

    protected byte[] stringBytes(String s) {
        return s == null ? new byte[0] : s.getBytes(java.nio.charset.StandardCharsets.UTF_8);
    }
}
