package com.example.webservice.controller;

import com.example.webservice.api.ApiResponse;
import com.example.webservice.dto.ClusterConfigDTO;
import com.example.webservice.service.ClusterConfigFileService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/v1/clusters")
public class ClusterConfigController {

    private static final Logger logger = LoggerFactory.getLogger(ClusterConfigController.class);
    private final ClusterConfigFileService fileService = new ClusterConfigFileService();

    @PostMapping
    public ApiResponse<String> createOrUpdate(@Validated @RequestBody ClusterConfigDTO request) {
        logger.info("接收到集群配置请求: mode={}, id={}, nicid={}, virtualip={}",
                   request.getMode(), request.getId(), request.getNicid(), request.getVirtualip());
        try {
            fileService.writeFromDto(request);
            logger.info("集群配置保存成功: id={}", request.getId());
            return ApiResponse.success("保存成功", null);
        } catch (Exception e) {
            logger.error("集群配置保存失败: id={}, error={}", request.getId(), e.getMessage(), e);
            return ApiResponse.error(500, "保存失败: " + e.getMessage());
        }
    }

    @GetMapping
    public ApiResponse<Object> get() {
        logger.info("接收到获取集群配置请求");
        try {
            Object result = fileService.readAllPlatforms();
            logger.debug("成功读取集群配置，返回数据: {}", result);
            return ApiResponse.success(result);
        } catch (Exception e) {
            logger.error("读取集群配置失败: {}", e.getMessage(), e);
            return ApiResponse.error(500, "读取失败: " + e.getMessage());
        }
    }
}


