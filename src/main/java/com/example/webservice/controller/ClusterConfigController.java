package com.example.webservice.controller;

import com.example.webservice.api.ApiResponse;
import com.example.webservice.dto.ClusterConfigDTO;
import com.example.webservice.service.ClusterConfigFileService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/v1/clusters")
public class ClusterConfigController {

    private final ClusterConfigFileService fileService = new ClusterConfigFileService();

    @PostMapping
    public ApiResponse<String> createOrUpdate(@Validated @RequestBody ClusterConfigDTO request) {
        try {
            fileService.writeFromDto(request);
            return ApiResponse.success("保存成功", null);
        } catch (Exception e) {
            return ApiResponse.error(500, "保存失败: " + e.getMessage());
        }
    }

    @GetMapping
    public ApiResponse<Object> get() {
        try {
            return ApiResponse.success(fileService.readAllPlatforms());
        } catch (Exception e) {
            return ApiResponse.error(500, "读取失败: " + e.getMessage());
        }
    }
}


