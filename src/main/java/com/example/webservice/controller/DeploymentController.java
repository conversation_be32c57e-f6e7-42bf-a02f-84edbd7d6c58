package com.example.webservice.controller;

import com.example.webservice.common.Constant;
import com.example.webservice.util.AesUtil;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.InetSocketAddress;
import java.net.Socket;

@RestController
@RequestMapping("/api/v1/deployments")
public class DeploymentController extends CommonController{

	@PostMapping(value = "/file", consumes = MediaType.APPLICATION_OCTET_STREAM_VALUE, produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
	public ResponseEntity<byte[]> deployFile(@RequestBody byte[] commandBytes) {
		byte[] plain;
		try {
			plain = AesUtil.decrypt(commandBytes);
		} catch (Exception e) {
			return ResponseEntity.status(HttpStatus.BAD_GATEWAY).body(stringBytes("解密失败: " + e.getMessage()));
		}
		return forwardPayload(plain, Constant.FILE_COMMAND_PORT);
	}

	@PostMapping(value = "/db", consumes = MediaType.APPLICATION_OCTET_STREAM_VALUE, produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
	public ResponseEntity<byte[]> deployDB(@RequestBody byte[] commandBytes) {
		byte[] plain;
		try {
			plain = AesUtil.decrypt(commandBytes);
		} catch (Exception e) {
			return ResponseEntity.status(HttpStatus.BAD_GATEWAY).body(stringBytes("解密失败: " + e.getMessage()));
		}
		return forwardPayload(plain,Constant.DB_COMMAND_PORT);
	}

	@PostMapping(value = "/mail", consumes = MediaType.APPLICATION_OCTET_STREAM_VALUE, produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
	public ResponseEntity<byte[]> deployMail(@RequestBody byte[] commandBytes) {
		byte[] plain;
		try {
			plain = AesUtil.decrypt(commandBytes);
		} catch (Exception e) {
			return ResponseEntity.status(HttpStatus.BAD_GATEWAY).body(stringBytes("解密失败: " + e.getMessage()));
		}
		return forwardPayload(plain,Constant.MAIL_COMMAND_PORT);
	}

	@PostMapping(value = "/mq", consumes = MediaType.APPLICATION_OCTET_STREAM_VALUE, produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
	public ResponseEntity<byte[]> deployMQ(@RequestBody byte[] commandBytes) {
		byte[] plain;
		try {
			plain = AesUtil.decrypt(commandBytes);
		} catch (Exception e) {
			return ResponseEntity.status(HttpStatus.BAD_GATEWAY).body(stringBytes("解密失败: " + e.getMessage()));
		}
		return forwardPayload(plain,Constant.MQ_COMMAND_PORT);
	}

	@PostMapping(value = "/onvif", consumes = MediaType.APPLICATION_OCTET_STREAM_VALUE, produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
	public ResponseEntity<byte[]> deployOnvif(@RequestBody byte[] commandBytes) {
		byte[] plain;
		try {
			plain = AesUtil.decrypt(commandBytes);
		} catch (Exception e) {
			return ResponseEntity.status(HttpStatus.BAD_GATEWAY).body(stringBytes("解密失败: " + e.getMessage()));
		}
		return forwardPayload(plain,Constant.ONVIF_COMMAND_PORT);
	}


}

