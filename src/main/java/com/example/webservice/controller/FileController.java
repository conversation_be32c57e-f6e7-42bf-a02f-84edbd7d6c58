package com.example.webservice.controller;

import com.example.webservice.api.ApiResponse;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/api/v1/files")
public class FileController {

	private static final long MAX_FILE_SIZE_BYTES = 1_048_576L; // 1MB
	private static final int MAX_FILES_PER_REQUEST = 5;

	@PostMapping(value = "/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
	public ResponseEntity<ApiResponse<String>> upload(@RequestParam("destPaths") List<String> destPaths,
														 @RequestParam("files") List<MultipartFile> files) {
		if (files == null || files.isEmpty()) {
			return ResponseEntity.status(HttpStatus.BAD_REQUEST)
					.body(ApiResponse.error(400, "至少上传一个文件"));
		}
		if (files.size() > MAX_FILES_PER_REQUEST) {
			return ResponseEntity.status(HttpStatus.BAD_REQUEST)
					.body(ApiResponse.error(400, "单次最多上传" + MAX_FILES_PER_REQUEST + "个文件"));
		}
		if (destPaths == null || destPaths.isEmpty() || destPaths.size() != files.size()) {
			return ResponseEntity.status(HttpStatus.BAD_REQUEST)
					.body(ApiResponse.error(400, "destPaths数量需与files一致"));
		}

		List<Path> saved = new ArrayList<Path>();
		try {
			for (int i = 0; i < files.size(); i++) {
				MultipartFile file = files.get(i);
				String dest = destPaths.get(i);
				if (file == null || file.isEmpty()) {
					throw new IOException("存在空文件");
				}
				if (file.getSize() > MAX_FILE_SIZE_BYTES) {
					throw new IOException("文件超出大小限制(<=1MB): " + file.getOriginalFilename());
				}
				if (dest == null || dest.trim().isEmpty()) {
					throw new IOException("destPaths包含空路径");
				}
				Path target = Paths.get(dest).normalize();
				Path parent = target.getParent();
				if (parent == null || !Files.isDirectory(parent)) {
					throw new IOException("目标目录不存在: " + (parent == null ? "null" : parent.toString()));
				}
				try (InputStream in = file.getInputStream()) {
					Files.copy(in, target, StandardCopyOption.REPLACE_EXISTING);
				}
				saved.add(target);
			}
			return ResponseEntity.ok(ApiResponse.success("文件全部保存成功", null));
		} catch (Exception e) {
			for (Path p : saved) {
				try {
					Files.deleteIfExists(p);
				} catch (IOException ignore) { }
			}
			return ResponseEntity.status(HttpStatus.BAD_REQUEST)
					.body(ApiResponse.error(400, "上传失败: " + e.getMessage()));
		}
	}
}


