package com.example.webservice.filter;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.springframework.lang.NonNull;
import java.io.IOException;
import java.util.Map;
import java.util.StringJoiner;

@Component
public class RequestLoggingFilter extends OncePerRequestFilter {

	private static final Logger logger = LoggerFactory.getLogger(RequestLoggingFilter.class);

	@Override
	protected void doFilterInternal(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response, @NonNull FilterChain filterChain)
			throws ServletException, IOException {
		long startNanos = System.nanoTime();

		String method = request.getMethod();
		String uri = request.getRequestURI();
		String query = request.getQueryString();
		String contentType = request.getContentType();
		String clientIp = extractClientIp(request);
		String paramsSummary = summarizeParameters(request);

		logger.info("收到请求: method={}, uri={}{}{}, clientIp={}, contentType={}, params={}",
				method,
				uri,
				query == null || query.isEmpty() ? "" : "?",
				query == null ? "" : query,
				clientIp,
				contentType,
				paramsSummary);

		try {
			filterChain.doFilter(request, response);
		} finally {
			long durationMs = (System.nanoTime() - startNanos) / 1_000_000;
			int status = response.getStatus();
			logger.info("请求完成: method={}, uri={}, status={}, durationMs={}", method, uri, status, durationMs);
		}
	}

	private String extractClientIp(HttpServletRequest request) {
		String header = request.getHeader("X-Forwarded-For");
		if (header != null && !header.isEmpty()) {
			int comma = header.indexOf(',');
			return comma > 0 ? header.substring(0, comma).trim() : header.trim();
		}
		header = request.getHeader("X-Real-IP");
		if (header != null && !header.isEmpty()) {
			return header.trim();
		}
		return request.getRemoteAddr();
	}

	private String summarizeParameters(HttpServletRequest request) {
		// Avoid reading body; only log query/form params to prevent binary/JSON payload logging
		String contentType = request.getContentType();
		boolean isForm = contentType != null && contentType.toLowerCase().contains("application/x-www-form-urlencoded");
		boolean isMultipart = contentType != null && contentType.toLowerCase().contains("multipart/form-data");
		boolean isBinary = contentType != null && contentType.toLowerCase().contains("application/octet-stream");

		if (isBinary || isMultipart) {
			return "<skipped>";
		}

		Map<String, String[]> paramMap = request.getParameterMap();
		if (paramMap == null || paramMap.isEmpty()) {
			return "{}";
		}
		StringJoiner joiner = new StringJoiner(", ", "{", "}");
		for (Map.Entry<String, String[]> entry : paramMap.entrySet()) {
			String key = maskIfSensitive(entry.getKey());
			String[] values = entry.getValue();
			if (values == null) {
				joiner.add(key + "=");
				continue;
			}
			String displayValue;
			if (isForm) {
				// Limit logged length for safety
				displayValue = truncate(String.join("|", values), 256);
			} else {
				displayValue = "<hidden>";
			}
			joiner.add(key + "=" + displayValue);
		}
		return joiner.toString();
	}

	private String truncate(String s, int maxLen) {
		if (s == null) return null;
		return s.length() <= maxLen ? s : s.substring(0, maxLen) + "...";
	}

	private String maskIfSensitive(String key) {
		String lower = key == null ? "" : key.toLowerCase();
		if (lower.contains("password") || lower.contains("token") || lower.contains("secret") || lower.contains("authorization")) {
			return key + "(masked)";
		}
		return key;
	}
}


