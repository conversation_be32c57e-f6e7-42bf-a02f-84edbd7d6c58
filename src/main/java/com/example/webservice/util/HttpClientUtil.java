package com.example.webservice.util;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.HashMap;
import java.util.Map;

public class HttpClientUtil {

	public static String simpleGet(String host, int port, String path, int timeoutMs) throws IOException {
		String urlStr = buildUrl(host, port, path);
		HttpURLConnection conn = null;
		try {
			URL url = new URL(urlStr);
			conn = (HttpURLConnection) url.openConnection();
			conn.setRequestMethod("GET");
			conn.setConnectTimeout(timeoutMs);
			conn.setReadTimeout(timeoutMs);
			conn.setDoInput(true);
			int code = conn.getResponseCode();
			if (code != 200) {
				throw new IOException("HTTP " + code);
			}
			BufferedReader reader = new BufferedReader(new InputStreamReader(conn.getInputStream()));
			StringBuilder sb = new StringBuilder();
			String line;
			while ((line = reader.readLine()) != null) {
				sb.append(line);
			}
			return sb.toString();
		} finally {
			if (conn != null) conn.disconnect();
		}
	}

	private static String buildUrl(String host, int port, String path) {
		String p = (path == null || path.isEmpty()) ? "/" : (path.startsWith("/") ? path : "/" + path);
		return "http://" + host + ":" + port + p;
	}

	// 通过HTTP获取状态并解析 key=value&key=value 形式
	public static Map<String, Integer> fetchHostStatus() throws IOException {
		String body = simpleGet("127.0.0.1", 9001, null, 2000);
		Map<String, Integer> result = new HashMap<String, Integer>();
		if (body == null || body.isEmpty()) return result;
		String[] pairs = body.split("&");
		for (String pair : pairs) {
			if (pair == null || pair.isEmpty()) continue;
			int idx = pair.indexOf('=');
			if (idx <= 0) continue;
			String key = pair.substring(0, idx);
			String val = pair.substring(idx + 1);
			try {
				result.put(key, Integer.parseInt(val));
			} catch (NumberFormatException ignore) {
				// 忽略非数字
			}
		}
		return result;
	}
}


