package com.example.webservice.util;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;

public final class AesUtil {

	private static final String TRANSFORMATION = "AES/CBC/PKCS5Padding";
	private static final byte[] KEY = "UDGAP-VENUS-3000".getBytes(StandardCharsets.UTF_8);;
	private static final byte[] IV = "unimasunimasunim".getBytes(StandardCharsets.UTF_8);;


	private AesUtil() {}

	public static byte[] decrypt(byte[] cipherBytes) throws Exception {
		if (cipherBytes == null) {
			return new byte[0];
		}
		Cipher cipher = Cipher.getInstance(TRANSFORMATION);
		SecretKeySpec keySpec = new SecretKeySpec(KEY, "AES");
		IvParameterSpec ivSpec = new IvParameterSpec(IV);
		cipher.init(Cipher.DECRYPT_MODE, keySpec, ivSpec);
		return cipher.doFinal(cipherBytes);
	}
	public static byte[] encrypt(byte[] plainBytes) throws Exception {
		if (plainBytes == null) {
			return new byte[0];
		}
		Cipher cipher = Cipher.getInstance(TRANSFORMATION);
		SecretKeySpec keySpec = new SecretKeySpec(KEY, "AES");
		IvParameterSpec ivSpec = new IvParameterSpec(IV);
		cipher.init(Cipher.ENCRYPT_MODE, keySpec, ivSpec);
		return cipher.doFinal(plainBytes);
	}
}


