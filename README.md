# Webservice Module (Java 8, Spring Boot)

## Quickstart
    mvn spring-boot:run

Service runs on http://localhost:8080

## Modules
- Deployment API
- Service Lifecycle API (start/stop/restart)
- Service Status API
- System Management API (reserved)
- Audit Alert API (reserved)
- Cluster Configuration API

## Build
    mvn clean package

## Notes
- Java 8 compatible (source/target 1.8)
- Controllers return a unified ApiResponse{code,message,data}
