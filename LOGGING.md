# 日志配置说明

## 日志文件位置
所有日志文件都会生成在程序当前目录的 `logs` 文件夹中：

```
./logs/
├── webservice.log              # 所有级别的日志
├── webservice-error.log        # 仅错误日志
├── webservice.2024-01-15.1.log # 按日期和大小滚动的历史日志
└── ...
```

## 日志级别配置
- **根日志级别**: INFO
- **应用包日志级别**: DEBUG (`com.example.webservice`)
- **Spring框架日志**: INFO
- **Web请求日志**: INFO

## 日志滚动策略
- **单个文件最大大小**: 10MB
- **历史文件保留天数**: 30天
- **所有日志文件总大小限制**: 1GB
- **错误日志总大小限制**: 500MB

## 日志格式
```
yyyy-MM-dd HH:mm:ss.SSS [线程名] 日志级别 类名 - 日志消息
```

示例：
```
2024-01-15 14:30:25.123 [http-nio-8181-exec-1] INFO  c.e.w.controller.ClusterConfigController - 接收到集群配置请求: mode=nor, id=201
```

## 环境配置
- **开发环境** (`dev`): DEBUG级别，更详细的日志
- **生产环境** (`prod`): WARN级别，减少日志输出

## 启动应用
使用提供的启动脚本会自动创建logs目录：

**Windows:**
```bash
start.bat
```

**Linux/Mac:**
```bash
chmod +x start.sh
./start.sh
```

**或者直接使用Maven:**
```bash
mkdir logs  # 手动创建logs目录
mvn spring-boot:run
```

## 日志特性
1. **异步日志**: 使用异步appender提高性能
2. **分级存储**: 错误日志单独存储便于排查
3. **自动滚动**: 按时间和大小自动滚动，避免单个文件过大
4. **UTF-8编码**: 支持中文日志内容
5. **控制台输出**: 开发时同时在控制台显示日志

## 修改日志配置
- 修改 `src/main/resources/application.yml` 中的 `logging` 部分进行简单配置
- 修改 `src/main/resources/logback-spring.xml` 进行高级配置
