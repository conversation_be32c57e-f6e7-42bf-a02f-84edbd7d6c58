#!/bin/bash
echo "Testing File Upload API..."

# Create test directory
mkdir -p test_uploads

# Create test files
echo "This is test file 1" > test_file1.txt
echo "This is test file 2" > test_file2.txt
echo "Binary test content" > test_binary.dat

echo
echo "=== Test 1: Single file upload ==="
curl -X POST \
  http://localhost:8181/api/v1/files/upload \
  -F "files=@test_file1.txt" \
  -F "destPaths=test_uploads/uploaded_file1.txt"

echo
echo
echo "=== Test 2: Multiple files upload ==="
curl -X POST \
  http://localhost:8181/api/v1/files/upload \
  -F "files=@test_file1.txt" \
  -F "files=@test_file2.txt" \
  -F "destPaths=test_uploads/multi_file1.txt" \
  -F "destPaths=test_uploads/multi_file2.txt"

echo
echo
echo "=== Test 3: Error test - Invalid destination ==="
curl -X POST \
  http://localhost:8181/api/v1/files/upload \
  -F "files=@test_file1.txt" \
  -F "destPaths=/invalid_dir/test.txt"

echo
echo
echo "=== Test 4: Error test - No files ==="
curl -X POST \
  http://localhost:8181/api/v1/files/upload \
  -F "destPaths=test_uploads/test.txt"

echo
echo
echo "Testing completed!"
echo "Check the test_uploads directory for uploaded files."

# Cleanup test files
rm -f test_file1.txt test_file2.txt test_binary.dat

echo "Test files cleaned up."
