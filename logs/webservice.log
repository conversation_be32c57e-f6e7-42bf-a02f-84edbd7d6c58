2025-09-11 15:27:53.052 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-09-11 15:27:53.055 [main] INFO  com.example.webservice.WebServiceApplication - Starting WebServiceApplication using Java 1.8.0_121 on DESKTOP-Q2GU3TI with PID 263412 (D:\history\cursor\webservice\target\classes started by admin in D:\history\cursor\webservice)
2025-09-11 15:27:53.056 [main] DEBUG com.example.webservice.WebServiceApplication - Running with Spring Boot v2.7.18, Spring v5.3.31
2025-09-11 15:27:53.056 [main] INFO  com.example.webservice.WebServiceApplication - No active profile set, falling back to 1 default profile: "default"
2025-09-11 15:27:54.049 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8181 (http)
2025-09-11 15:27:54.056 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8181"]
2025-09-11 15:27:54.057 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-09-11 15:27:54.058 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-09-11 15:27:54.166 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-09-11 15:27:54.166 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1065 ms
2025-09-11 15:27:54.694 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-09-11 15:27:54.716 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8181"]
2025-09-11 15:27:54.733 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8181 (http) with context path ''
2025-09-11 15:27:54.750 [main] INFO  com.example.webservice.WebServiceApplication - Started WebServiceApplication in 2.253 seconds (JVM running for 2.943)
2025-09-11 15:27:54.755 [main] INFO  com.example.webservice.WebServiceApplication - WebService应用启动完成！
2025-09-11 15:27:55.289 [RMI TCP Connection(2)-**********] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-11 15:27:55.290 [RMI TCP Connection(2)-**********] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-09-11 15:27:55.292 [RMI TCP Connection(2)-**********] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-09-11 15:29:18.511 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-09-11 15:29:18.520 [main] INFO  com.example.webservice.WebServiceApplication - Starting WebServiceApplication using Java 1.8.0_121 on DESKTOP-Q2GU3TI with PID 271500 (D:\history\cursor\webservice\target\classes started by admin in D:\history\cursor\webservice)
2025-09-11 15:29:18.520 [main] DEBUG com.example.webservice.WebServiceApplication - Running with Spring Boot v2.7.18, Spring v5.3.31
2025-09-11 15:29:18.521 [main] INFO  com.example.webservice.WebServiceApplication - No active profile set, falling back to 1 default profile: "default"
2025-09-11 15:29:19.818 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8181 (http)
2025-09-11 15:29:19.830 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8181"]
2025-09-11 15:29:19.832 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-09-11 15:29:19.833 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-09-11 15:29:20.034 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-09-11 15:29:20.034 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1462 ms
2025-09-11 15:29:21.008 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-09-11 15:29:21.034 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8181"]
2025-09-11 15:29:21.079 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8181 (http) with context path ''
2025-09-11 15:29:21.097 [main] INFO  com.example.webservice.WebServiceApplication - Started WebServiceApplication in 3.287 seconds (JVM running for 4.524)
2025-09-11 15:29:21.109 [main] INFO  com.example.webservice.WebServiceApplication - WebService应用启动完成！
2025-09-11 15:29:21.889 [RMI TCP Connection(3)-**********] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-11 15:29:21.889 [RMI TCP Connection(3)-**********] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-09-11 15:29:21.890 [RMI TCP Connection(3)-**********] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-09-11 15:34:08.939 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-09-11 15:34:08.946 [main] INFO  com.example.webservice.WebServiceApplication - Starting WebServiceApplication using Java 1.8.0_121 on DESKTOP-Q2GU3TI with PID 18080 (D:\history\cursor\webservice\target\classes started by admin in D:\history\cursor\webservice)
2025-09-11 15:34:08.947 [main] DEBUG com.example.webservice.WebServiceApplication - Running with Spring Boot v2.7.18, Spring v5.3.31
2025-09-11 15:34:08.948 [main] INFO  com.example.webservice.WebServiceApplication - No active profile set, falling back to 1 default profile: "default"
2025-09-11 15:34:09.954 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8181 (http)
2025-09-11 15:34:09.960 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8181"]
2025-09-11 15:34:09.961 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-09-11 15:34:09.961 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-09-11 15:34:10.061 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-09-11 15:34:10.061 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1068 ms
2025-09-11 15:34:10.523 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-09-11 15:34:10.545 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8181"]
2025-09-11 15:34:10.561 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8181 (http) with context path ''
2025-09-11 15:34:10.572 [main] INFO  com.example.webservice.WebServiceApplication - Started WebServiceApplication in 2.15 seconds (JVM running for 2.913)
2025-09-11 15:34:10.576 [main] INFO  com.example.webservice.WebServiceApplication - WebService应用启动完成！
2025-09-11 15:34:11.133 [RMI TCP Connection(1)-**********] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-11 15:34:11.133 [RMI TCP Connection(1)-**********] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-09-11 15:34:11.134 [RMI TCP Connection(1)-**********] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-09-11 15:36:05.882 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-09-11 15:36:05.891 [main] INFO  com.example.webservice.WebServiceApplication - Starting WebServiceApplication using Java 1.8.0_121 on DESKTOP-Q2GU3TI with PID 452484 (D:\history\cursor\webservice\target\classes started by admin in D:\history\cursor\webservice)
2025-09-11 15:36:05.891 [main] DEBUG com.example.webservice.WebServiceApplication - Running with Spring Boot v2.7.18, Spring v5.3.31
2025-09-11 15:36:05.891 [main] INFO  com.example.webservice.WebServiceApplication - No active profile set, falling back to 1 default profile: "default"
2025-09-11 15:36:07.029 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8181 (http)
2025-09-11 15:36:07.039 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8181"]
2025-09-11 15:36:07.041 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-09-11 15:36:07.041 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-09-11 15:36:07.213 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-09-11 15:36:07.213 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1274 ms
2025-09-11 15:36:08.024 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-09-11 15:36:08.046 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8181"]
2025-09-11 15:36:08.086 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8181 (http) with context path ''
2025-09-11 15:36:08.102 [main] INFO  com.example.webservice.WebServiceApplication - Started WebServiceApplication in 2.788 seconds (JVM running for 3.731)
2025-09-11 15:36:08.114 [main] INFO  com.example.webservice.WebServiceApplication - WebService应用启动完成！
2025-09-11 15:36:08.547 [RMI TCP Connection(3)-**********] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-11 15:36:08.548 [RMI TCP Connection(3)-**********] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-09-11 15:36:08.549 [RMI TCP Connection(3)-**********] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-09-11 15:38:15.044 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-09-11 15:38:15.044 [main] INFO  com.example.webservice.WebServiceApplication - Starting WebServiceApplication using Java 1.8.0_121 on DESKTOP-Q2GU3TI with PID 740044 (D:\history\cursor\webservice\target\classes started by admin in D:\history\cursor\webservice)
2025-09-11 15:38:15.044 [main] DEBUG com.example.webservice.WebServiceApplication - Running with Spring Boot v2.7.18, Spring v5.3.31
2025-09-11 15:38:15.045 [main] INFO  com.example.webservice.WebServiceApplication - No active profile set, falling back to 1 default profile: "default"
2025-09-11 15:38:15.815 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8181 (http)
2025-09-11 15:38:15.819 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8181"]
2025-09-11 15:38:15.820 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-09-11 15:38:15.820 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-09-11 15:38:15.910 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-09-11 15:38:15.911 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 832 ms
2025-09-11 15:38:16.342 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-09-11 15:38:16.360 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8181"]
2025-09-11 15:38:16.377 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8181 (http) with context path ''
2025-09-11 15:38:16.389 [main] INFO  com.example.webservice.WebServiceApplication - Started WebServiceApplication in 1.763 seconds (JVM running for 2.439)
2025-09-11 15:38:16.393 [main] INFO  com.example.webservice.WebServiceApplication - WebService应用启动完成！
2025-09-11 15:38:16.736 [RMI TCP Connection(2)-**********] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-11 15:38:16.736 [RMI TCP Connection(2)-**********] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-09-11 15:38:16.737 [RMI TCP Connection(2)-**********] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-09-11 15:40:23.039 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-09-11 15:40:23.042 [main] INFO  com.example.webservice.WebServiceApplication - Starting WebServiceApplication using Java 1.8.0_121 on DESKTOP-Q2GU3TI with PID 974540 (D:\history\cursor\webservice\target\classes started by admin in D:\history\cursor\webservice)
2025-09-11 15:40:23.042 [main] DEBUG com.example.webservice.WebServiceApplication - Running with Spring Boot v2.7.18, Spring v5.3.31
2025-09-11 15:40:23.043 [main] INFO  com.example.webservice.WebServiceApplication - No active profile set, falling back to 1 default profile: "default"
2025-09-11 15:40:23.952 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8181 (http)
2025-09-11 15:40:23.959 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8181"]
2025-09-11 15:40:23.960 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-09-11 15:40:23.960 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-09-11 15:40:24.057 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-09-11 15:40:24.057 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 976 ms
2025-09-11 15:40:24.509 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-09-11 15:40:24.525 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8181"]
2025-09-11 15:40:24.540 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8181 (http) with context path ''
2025-09-11 15:40:24.551 [main] INFO  com.example.webservice.WebServiceApplication - Started WebServiceApplication in 1.946 seconds (JVM running for 2.694)
2025-09-11 15:40:24.555 [main] INFO  com.example.webservice.WebServiceApplication - WebService应用启动完成！
2025-09-11 15:40:24.834 [RMI TCP Connection(3)-**********] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-11 15:40:24.835 [RMI TCP Connection(3)-**********] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-09-11 15:40:24.836 [RMI TCP Connection(3)-**********] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-09-11 15:40:32.695 [http-nio-8181-exec-1] INFO  c.e.webservice.service.ClusterConfigFileService - cfDevid: , devid: unimas20250303
2025-09-11 15:40:54.648 [http-nio-8181-exec-2] INFO  c.e.webservice.service.ClusterConfigFileService - cfDevid: , devid: unimas20250303
